#!/usr/bin/env python3
"""
Sync .ai/tasks folder structure with GitHub Projects.
Creates issues, updates project boards, and maintains status sync.
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, List, Optional
import requests
from datetime import datetime

class GitHubProjectSync:
    def __init__(self, github_token: str, repo_owner: str, repo_name: str):
        self.token = github_token
        self.owner = repo_owner
        self.repo = repo_name
        self.base_url = "https://api.github.com"
        self.headers = {
            "Authorization": f"token {github_token}",
            "Accept": "application/vnd.github.v3+json"
        }
        
    def get_tasks_from_folder(self, folder_path: str) -> List[Dict]:
        """Parse task files from .ai/tasks folders."""
        tasks = []
        task_folder = Path(folder_path)
        
        if not task_folder.exists():
            return tasks
            
        for task_file in task_folder.glob("*.md"):
            task_data = self.parse_task_file(task_file)
            if task_data:
                task_data['file_path'] = str(task_file)
                task_data['status'] = self.determine_status_from_path(task_file)
                tasks.append(task_data)
                
        return tasks
    
    def parse_task_file(self, file_path: Path) -> Optional[Dict]:
        """Parse a task markdown file and extract metadata."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Extract front matter if present
            if content.startswith('---'):
                parts = content.split('---', 2)
                if len(parts) >= 3:
                    front_matter = yaml.safe_load(parts[1])
                    body = parts[2].strip()
                else:
                    front_matter = {}
                    body = content
            else:
                front_matter = {}
                body = content
            
            # Extract title (first # heading)
            lines = body.split('\n')
            title = None
            for line in lines:
                if line.startswith('# '):
                    title = line[2:].strip()
                    break
            
            # Determine classification from title or filename
            classification = self.determine_classification(title or file_path.name, body)
            
            return {
                'title': title or file_path.stem,
                'body': body,
                'classification': classification,
                'metadata': front_matter,
                'filename': file_path.name
            }
            
        except Exception as e:
            print(f"Error parsing {file_path}: {e}")
            return None
    
    def determine_classification(self, title: str, body: str) -> str:
        """Determine if task is agent-ready, agent-assisted, or human-only."""
        title_lower = title.lower()
        body_lower = body.lower()
        
        if '🤖' in title or 'agent-ready' in body_lower:
            return 'agent-ready'
        elif '👥' in title or 'agent-assisted' in body_lower:
            return 'agent-assisted'
        elif '👨‍💻' in title or 'human-only' in body_lower:
            return 'human-only'
        
        # Auto-classify based on content
        agent_ready_indicators = [
            'api endpoint', 'unit test', 'configuration', 'schema',
            'crud operations', 'data model', 'validation'
        ]
        
        if any(indicator in body_lower for indicator in agent_ready_indicators):
            return 'agent-ready'
        
        return 'agent-assisted'  # Default
    
    def determine_status_from_path(self, file_path: Path) -> str:
        """Determine task status based on folder location."""
        path_str = str(file_path)
        if '/backlog/' in path_str:
            return 'backlog'
        elif '/doing/' in path_str:
            return 'in-progress'
        elif '/done/' in path_str:
            return 'done'
        return 'backlog'
    
    def create_github_issue(self, task: Dict) -> Optional[Dict]:
        """Create a GitHub issue from task data."""
        url = f"{self.base_url}/repos/{self.owner}/{self.repo}/issues"
        
        # Prepare labels based on classification
        labels = ['type:feature']
        if task['classification'] == 'agent-ready':
            labels.append('agent-ready')
        elif task['classification'] == 'agent-assisted':
            labels.append('agent-assisted')
        else:
            labels.append('human-only')
        
        # Add phase label if found in title
        title = task['title']
        if '[Phase' in title or 'Phase' in title:
            # Extract phase number
            import re
            phase_match = re.search(r'Phase (\d+\.\d+)', title)
            if phase_match:
                labels.append(f"phase:{phase_match.group(1)}")
        
        issue_data = {
            'title': task['title'],
            'body': task['body'],
            'labels': labels
        }
        
        try:
            response = requests.post(url, headers=self.headers, json=issue_data)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error creating issue: {e}")
            return None
    
    def update_project_board(self, issue_data: Dict, status: str):
        """Update GitHub Projects board with issue status."""
        # This would require GitHub Projects API v2 (GraphQL)
        # Implementation depends on your specific project setup
        print(f"Would update project board: Issue #{issue_data['number']} -> {status}")
    
    def sync_all_tasks(self):
        """Main sync function - sync all tasks across all folders."""
        ai_tasks_path = Path('.ai/tasks')
        
        if not ai_tasks_path.exists():
            print("No .ai/tasks folder found")
            return
        
        # Get all tasks from all folders
        all_tasks = []
        for folder in ['backlog', 'doing']:
            folder_path = ai_tasks_path / folder
            tasks = self.get_tasks_from_folder(str(folder_path))
            all_tasks.extend(tasks)
        
        # Handle done tasks (multiple sprint folders)
        done_path = ai_tasks_path / 'done'
        if done_path.exists():
            for sprint_folder in done_path.iterdir():
                if sprint_folder.is_dir():
                    tasks = self.get_tasks_from_folder(str(sprint_folder))
                    all_tasks.extend(tasks)
        
        print(f"Found {len(all_tasks)} tasks to sync")
        
        # Create GitHub issues for tasks that don't exist
        for task in all_tasks:
            if task['status'] in ['backlog', 'in-progress']:  # Don't create issues for done tasks
                print(f"Processing: {task['title']}")
                issue = self.create_github_issue(task)
                if issue:
                    print(f"Created issue #{issue['number']}: {task['title']}")
                    self.update_project_board(issue, task['status'])

def main():
    """Main function to run the sync."""
    # Get credentials from environment
    github_token = os.getenv('GITHUB_TOKEN')
    repo_owner = os.getenv('GITHUB_OWNER', 'your-username')  # Update default
    repo_name = os.getenv('GITHUB_REPO', 'markdown-mill-local')  # Update default
    
    if not github_token:
        print("Error: GITHUB_TOKEN environment variable required")
        return
    
    syncer = GitHubProjectSync(github_token, repo_owner, repo_name)
    syncer.sync_all_tasks()

if __name__ == "__main__":
    main() 