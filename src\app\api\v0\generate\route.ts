import { generateV0UI, streamV0Text } from '@/lib/v0-client';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { prompt, stream = false, model } = await request.json();

    if (!prompt) {
      return NextResponse.json({ error: 'Prompt is required' }, { status: 400 });
    }

    if (stream) {
      // Return streaming response
      const result = await streamV0Text({
        prompt,
        model,
        systemPrompt: `You are v0, an AI assistant specialized in creating modern React components using Next.js, TypeScript, and Tailwind CSS.

When generating components:
1. Use TypeScript with proper type definitions
2. Use Tailwind CSS for styling
3. Follow React best practices
4. Make components responsive and accessible
5. Include proper prop types and interfaces
6. Use modern React patterns (hooks, functional components)
7. Ensure code is production-ready

Generate clean, maintainable, and well-structured React components.`,
      });

      // Convert the streaming result to a Response
      return new Response(result.toDataStream(), {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Cache-Control': 'no-cache',
        },
      });
    } else {
      // Return regular response
      const result = await generateV0UI({
        prompt,
        model,
      });

      return NextResponse.json({
        text: result.text,
        usage: result.usage,
        model: result.experimental_providerMetadata?.anthropic?.cacheCreationInputTokens,
      });
    }
  } catch (error) {
    console.error('v0 API Error:', error);

    if (error instanceof Error && error.message.includes('V0_API_KEY')) {
      return NextResponse.json(
        { error: 'v0 API key is not configured. Please set V0_API_KEY environment variable.' },
        { status: 500 },
      );
    }

    return NextResponse.json({ error: 'Failed to generate content with v0' }, { status: 500 });
  }
}

// Optional: GET endpoint for health check
export async function GET() {
  return NextResponse.json({
    status: 'v0 API integration is ready',
    models: ['v0-1.5-md', 'v0-1.5-lg', 'v0-1.0-md'],
    apiKeyConfigured: !!process.env.V0_API_KEY,
  });
}
