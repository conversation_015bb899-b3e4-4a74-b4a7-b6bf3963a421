import { generateText, generateObject, streamText } from 'ai';
import { vercel } from '@ai-sdk/vercel';

// v0 API Client Configuration
const v0Models = {
  'v0-1.5-md': 'v0-1.5-md', // For everyday tasks and UI generation
  'v0-1.5-lg': 'v0-1.5-lg', // For advanced thinking or reasoning
  'v0-1.0-md': 'v0-1.0-md', // Legacy model
} as const;

export type V0Model = keyof typeof v0Models;

// Default model
export const DEFAULT_V0_MODEL: V0Model = 'v0-1.5-md';

/**
 * Generate text using v0 API
 */
export async function generateV0Text({
  prompt,
  model = DEFAULT_V0_MODEL,
  systemPrompt,
}: {
  prompt: string;
  model?: V0Model;
  systemPrompt?: string;
}) {
  if (!process.env.V0_API_KEY) {
    throw new Error('V0_API_KEY environment variable is required');
  }

  const messages = [];
  
  if (systemPrompt) {
    messages.push({ role: 'system' as const, content: systemPrompt });
  }
  
  messages.push({ role: 'user' as const, content: prompt });

  const result = await generateText({
    model: vercel(v0Models[model]),
    messages,
  });

  return result;
}

/**
 * Generate structured object using v0 API
 */
export async function generateV0Object<T>({
  prompt,
  schema,
  model = DEFAULT_V0_MODEL,
  systemPrompt,
}: {
  prompt: string;
  schema: any; // Zod schema
  model?: V0Model;
  systemPrompt?: string;
}) {
  if (!process.env.V0_API_KEY) {
    throw new Error('V0_API_KEY environment variable is required');
  }

  const messages = [];
  
  if (systemPrompt) {
    messages.push({ role: 'system' as const, content: systemPrompt });
  }
  
  messages.push({ role: 'user' as const, content: prompt });

  const result = await generateObject({
    model: vercel(v0Models[model]),
    messages,
    schema,
  });

  return result;
}

/**
 * Stream text using v0 API
 */
export async function streamV0Text({
  prompt,
  model = DEFAULT_V0_MODEL,
  systemPrompt,
}: {
  prompt: string;
  model?: V0Model;
  systemPrompt?: string;
}) {
  if (!process.env.V0_API_KEY) {
    throw new Error('V0_API_KEY environment variable is required');
  }

  const messages = [];
  
  if (systemPrompt) {
    messages.push({ role: 'system' as const, content: systemPrompt });
  }
  
  messages.push({ role: 'user' as const, content: prompt });

  const result = await streamText({
    model: vercel(v0Models[model]),
    messages,
  });

  return result;
}

/**
 * Generate UI components using v0 API
 */
export async function generateV0UI({
  prompt,
  model = DEFAULT_V0_MODEL,
}: {
  prompt: string;
  model?: V0Model;
}) {
  const systemPrompt = `You are v0, an AI assistant specialized in creating modern React components using Next.js, TypeScript, and Tailwind CSS.

When generating components:
1. Use TypeScript with proper type definitions
2. Use Tailwind CSS for styling
3. Follow React best practices
4. Make components responsive and accessible
5. Include proper prop types and interfaces
6. Use modern React patterns (hooks, functional components)
7. Ensure code is production-ready

Generate clean, maintainable, and well-structured React components.`;

  return generateV0Text({
    prompt,
    model,
    systemPrompt,
  });
} 