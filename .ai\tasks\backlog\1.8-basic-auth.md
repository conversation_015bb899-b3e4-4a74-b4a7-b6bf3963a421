---
phase: "1.8"
classification: "agent-ready"
complexity: "medium"
estimated_hours: 6
dependencies: ["#1.1", "#1.7"]
assignee: "devin-ai"
created: 2024-01-15
---

# 🤖 Phase 1.8: Basic Authentication (Magic Link)

## Agent Assignment: READY
**Estimated Complexity:** Medium (6 hours)
**Dependencies:** Upload UI (#1.1), Frontend Results Page (#1.7)

## Background Context
Implement a simple, user-friendly authentication system using magic-link email login for the MVP. This provides basic user identification and session management without complex password requirements. Users enter their email, receive a secure login link, and can access their upload history and results.

## Acceptance Criteria
- [ ] Email input form with validation
- [ ] Magic link generation and email sending
- [ ] Secure token validation and session creation
- [ ] User session management (login/logout)
- [ ] Protected routes for authenticated users
- [ ] Email template for magic links
- [ ] Token expiration handling (15 minutes)
- [ ] Rate limiting for email requests

## Implementation Guide
**Code patterns to follow:** `.ai/rules/auth-security-standards.md`
**Reference implementations:** `.ai/references/magic-link-patterns.md`
**Dependencies:** Azure Static Web Apps auth, SendGrid, JWT, crypto
**File locations:** 
- `src/components/LoginForm.jsx`
- `src/hooks/useAuth.js`
- `api/auth/send-magic-link.js`
- `api/auth/verify-token.js`
- `src/contexts/AuthContext.jsx`

## Test Requirements
- Unit tests for token generation and validation
- Integration tests with email service
- Security tests (token tampering, replay attacks)
- Rate limiting tests
- User flow tests (login, logout, session persistence)
- Email template rendering tests

## Definition of Done
- [ ] Authentication system implemented and tested
- [ ] All tests passing (unit, integration, security)
- [ ] Email delivery working reliably
- [ ] Session management working correctly
- [ ] Rate limiting preventing abuse
- [ ] Security review completed

## Technical Specifications
```typescript
interface AuthUser {
  id: string;
  email: string;
  created_at: string;
  last_login: string;
  upload_count: number;
}

interface MagicLinkToken {
  email: string;
  token: string;
  expires_at: string;
  used: boolean;
  created_at: string;
}
```

**API Endpoints:**
```javascript
// POST /api/auth/send-magic-link
{
  "email": "<EMAIL>"
}

// GET /api/auth/verify?token=abc123&email=<EMAIL>
// Returns: JWT token or error

// POST /api/auth/logout
// Invalidates current session
```

**Magic Link Format:**
```
https://docstream.app/auth/verify?token={secure_token}&email={encoded_email}
```

**JWT Payload:**
```json
{
  "sub": "user_id",
  "email": "<EMAIL>", 
  "iat": 1642234567,
  "exp": 1642320967,
  "iss": "docstream-cloud"
}
```

## Environment Variables
- `SENDGRID_API_KEY`
- `JWT_SECRET`
- `MAGIC_LINK_EXPIRY_MINUTES=15`
- `RATE_LIMIT_REQUESTS_PER_HOUR=5`
- `FRONTEND_BASE_URL`

## Notes for AI Agent
- Use Azure Static Web Apps built-in auth if possible, otherwise implement custom
- Generate cryptographically secure tokens (32 bytes, base64url encoded)
- Implement proper CSRF protection
- Use secure, httpOnly cookies for session tokens
- Include proper email validation and sanitization
- Implement exponential backoff for failed login attempts
- Use professional email templates with clear branding
- Include proper logging for security events
- Handle edge cases (invalid emails, expired tokens)

## Email Template
```html
<!DOCTYPE html>
<html>
<head>
    <title>DocStream Cloud - Login Link</title>
</head>
<body>
    <h2>Your DocStream Cloud Login Link</h2>
    <p>Click the link below to access your account:</p>
    <a href="{magic_link}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px;">
        Login to DocStream Cloud
    </a>
    <p>This link expires in 15 minutes for security.</p>
    <p>If you didn't request this login, please ignore this email.</p>
</body>
</html>
```

## Security Considerations
- **Token Security**: Use cryptographically secure random tokens
- **Rate Limiting**: Prevent email spam and brute force attacks
- **Token Expiration**: Short-lived tokens (15 minutes)
- **Single Use**: Tokens can only be used once
- **HTTPS Only**: All auth endpoints must use HTTPS
- **Input Validation**: Sanitize and validate all email inputs
- **Audit Logging**: Log all authentication events

## User Experience Flow
1. **Login Request**: User enters email address
2. **Email Sent**: Magic link sent to user's email
3. **Link Click**: User clicks link in email
4. **Token Validation**: Server validates token and creates session
5. **Redirect**: User redirected to dashboard or original destination
6. **Session Active**: User can access protected features
7. **Logout**: User can explicitly logout or session expires

## Rate Limiting Strategy
- **Per Email**: Max 5 requests per hour per email address
- **Per IP**: Max 20 requests per hour per IP address
- **Global**: Max 1000 requests per hour globally
- **Backoff**: Exponential backoff for repeated requests
- **Monitoring**: Alert on unusual patterns 