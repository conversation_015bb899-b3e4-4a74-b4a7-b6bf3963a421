---
phase: "1.2"
classification: "agent-ready"
complexity: "medium"
estimated_hours: 4
dependencies: ["#1.1"]
assignee: "devin-ai"
created: 2024-01-15
---

# 🤖 Phase 1.2: Blob Trigger Function

## Agent Assignment: READY
**Estimated Complexity:** Medium (4 hours)
**Dependencies:** Upload UI (#1.1)

## Background Context
This Azure Function automatically triggers when PDFs are uploaded to the `incoming-pdfs/` container. It validates the upload, extracts metadata, and queues a message for pipeline processing. This decouples the upload process from pipeline execution for better scalability.

## Acceptance Criteria
- [ ] Azure Function with Blob Storage trigger on `incoming-pdfs/` container
- [ ] PDF validation (file type, size, corruption check)
- [ ] Extract basic metadata (filename, size, upload timestamp, user ID)
- [ ] Queue message to Service Bus with processing parameters
- [ ] Error handling with retry logic
- [ ] Logging for monitoring and debugging
- [ ] Dead letter queue for failed messages

## Implementation Guide
**Code patterns to follow:** `.ai/rules/azure-function-standards.md`
**Reference implementations:** `.ai/references/azure-servicebus-patterns.md`
**Dependencies:** Azure Functions SDK, Azure Service Bus SDK, PyPDF2
**File locations:** 
- `functions/blob_trigger/__init__.py`
- `functions/blob_trigger/function.json`
- `shared/validation.py`
- `shared/queue_client.py`

## Test Requirements
- Unit tests for PDF validation logic
- Integration test with Blob Storage trigger
- Service Bus message format validation
- Error handling tests (corrupted PDFs, oversized files)
- Dead letter queue functionality tests
- Performance tests (concurrent uploads)

## Definition of Done
- [ ] Function deployed and responding to blob events
- [ ] All tests passing (unit and integration)
- [ ] Monitoring and alerting configured
- [ ] Error handling covers all edge cases
- [ ] Documentation updated
- [ ] Performance benchmarks met (< 30s processing time)

## Technical Specifications
```python
# Message format for Service Bus queue
{
    "document_id": "uuid4",
    "blob_uri": "https://storage.../incoming-pdfs/file.pdf",
    "user_id": "user_uuid",
    "filename": "original_filename.pdf",
    "file_size_bytes": 1024000,
    "upload_timestamp": "2024-01-15T10:30:00Z",
    "processing_config": {
        "enable_abcde_extraction": true,
        "enable_causal_prescoring": true,
        "chunk_size": 500,
        "chunk_overlap": 100
    }
}
```

**Queue Configuration:**
- Queue name: `pipeline-processing-queue`
- Message TTL: 24 hours
- Dead letter queue: `pipeline-processing-dlq`
- Max delivery count: 3

**Environment Variables:**
- `STORAGE_CONNECTION_STRING`
- `SERVICEBUS_CONNECTION_STRING`
- `INCOMING_CONTAINER_NAME=incoming-pdfs`

## Notes for AI Agent
- Use Azure Functions Python v2 programming model
- Implement exponential backoff for retries
- Include correlation IDs for request tracing
- Validate PDF structure using PyPDF2 before queuing
- Set appropriate function timeout (5 minutes)
- Use structured logging with JSON format
- Handle concurrent blob events gracefully 