---
phase: "1.5"
classification: "agent-ready"
complexity: "medium"
estimated_hours: 6
dependencies: ["#1.4"]
assignee: "devin-ai"
created: 2024-01-15
---

# 🤖 Phase 1.5: Package and Link Results Function

## Agent Assignment: READY
**Estimated Complexity:** Medium (6 hours)
**Dependencies:** Pipeline Endpoint Trigger Function (#1.4)

## Background Context
After the AML pipeline completes processing, the outputs are scattered across different blob containers (markdown, embeddings, metadata JSON). This function collects all outputs for a document, packages them into a single ZIP file, and creates a time-limited SAS link for user download. This provides a clean, user-friendly way to deliver all pipeline results.

## Acceptance Criteria
- [ ] Function triggered when pipeline job status changes to "completed"
- [ ] Collect all pipeline outputs (markdown, embeddings JSONL, metadata JSON)
- [ ] Create ZIP file with organized folder structure
- [ ] Generate time-limited SAS token for download (24 hours)
- [ ] Update job status table with download link
- [ ] Handle missing or corrupted output files gracefully
- [ ] Clean up temporary files and old ZIP archives
- [ ] Send notification (email/webhook) when results are ready

## Implementation Guide
**Code patterns to follow:** `.ai/rules/azure-function-standards.md`
**Reference implementations:** `.ai/references/azure-blob-packaging-patterns.md`
**Dependencies:** Azure Functions SDK, zipfile, azure-storage-blob
**File locations:** 
- `functions/package_results/__init__.py`
- `functions/package_results/function.json`
- `shared/result_packager.py`
- `shared/notification_client.py`

## Test Requirements
- Unit tests for ZIP packaging logic
- Integration test with blob storage operations
- Error handling tests (missing files, corrupted outputs)
- SAS token generation and validation tests
- Cleanup and archival tests
- Performance tests (large file handling)

## Definition of Done
- [ ] Function deployed and responding to job completion events
- [ ] All tests passing (unit and integration)
- [ ] ZIP files created with correct structure and contents
- [ ] SAS links working and time-limited correctly
- [ ] Cleanup processes working (temp files, old archives)
- [ ] Monitoring and alerting configured

## Technical Specifications
```python
# ZIP File Structure
document_results_{document_id}.zip
├── README.txt                    # Processing summary
├── original/
│   └── document.pdf             # Original uploaded file
├── processed/
│   ├── document.md              # Markdown output
│   ├── document_embeddings.jsonl # Vector embeddings
│   ├── document_meta.json       # Processing metadata
│   └── causal_report.txt        # Causality analysis (if enabled)
└── logs/
    └── processing_log.txt       # Pipeline execution logs
```

**Function Trigger:**
```json
{
    "bindings": [
        {
            "name": "statusUpdate",
            "type": "eventGridTrigger",
            "direction": "in"
        }
    ],
    "scriptFile": "__init__.py"
}
```

**Result Manifest Schema:**
```python
{
    "document_id": "uuid4",
    "processing_completed": "2024-01-15T10:45:00Z",
    "download_url": "https://storage.../results/doc_results.zip?sas_token",
    "download_expires": "2024-01-16T10:45:00Z",
    "file_size_bytes": 2048000,
    "contents": {
        "markdown": "document.md",
        "embeddings": "document_embeddings.jsonl", 
        "metadata": "document_meta.json",
        "causal_report": "causal_report.txt",
        "original_pdf": "document.pdf"
    },
    "processing_summary": {
        "total_chunks": 45,
        "processing_time_seconds": 840,
        "features_enabled": ["abcde_extraction", "causal_prescoring"]
    }
}
```

**Environment Variables:**
- `STORAGE_CONNECTION_STRING`
- `RESULTS_CONTAINER_NAME=results`
- `TEMP_CONTAINER_NAME=temp-packaging`
- `SAS_EXPIRY_HOURS=24`
- `NOTIFICATION_WEBHOOK_URL` (optional)

## Notes for AI Agent
- Use Azure Functions Python v2 programming model
- Implement streaming ZIP creation for large files to avoid memory issues
- Generate descriptive README.txt with processing summary
- Include error handling for partial results (some files missing)
- Use correlation IDs for tracing across the packaging process
- Implement cleanup job to remove old ZIP files (>7 days)
- Add file integrity checks (checksums) in the manifest
- Use managed identity for secure blob storage access
- Include comprehensive logging for debugging

## File Collection Strategy
1. **Query job status table** for completed job details
2. **Locate output files** using result_locations from AML endpoint response
3. **Download files to temp storage** with validation
4. **Create ZIP archive** with organized structure
5. **Upload ZIP to results container** with appropriate metadata
6. **Generate SAS token** with 24-hour expiry
7. **Update job status** with download link
8. **Send notification** (if configured)
9. **Clean up temp files**

## Error Handling
- **Missing output files**: Include partial results with warning in README
- **Corrupted files**: Skip corrupted files, log error, continue packaging
- **Storage errors**: Retry with exponential backoff
- **ZIP creation failures**: Clean up partial files, mark job as failed
- **SAS token errors**: Log error, provide alternative access method 