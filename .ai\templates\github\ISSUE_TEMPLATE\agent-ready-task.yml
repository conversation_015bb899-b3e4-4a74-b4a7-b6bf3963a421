name: 🤖 Agent-Ready Task
description: Task that can be completed autonomously by an AI agent
title: "🤖 [Phase X.X] "
labels: ["agent-ready", "type:feature"]
projects: ["DocStream-Cloud Development"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        ## Agent Assignment Classification
        This task has been pre-classified as **AGENT_READY** based on:
        - ✅ Well-defined scope and acceptance criteria
        - ✅ Clear input/output specifications  
        - ✅ Minimal architectural decisions required
        - ✅ Existing patterns to follow
        - ✅ Can be tested programmatically

  - type: input
    id: phase
    attributes:
      label: "Roadmap Phase"
      description: "Which phase from the DocStream-Cloud roadmap (e.g., 1.1, 2.3)"
      placeholder: "1.1"
    validations:
      required: true

  - type: dropdown
    id: complexity
    attributes:
      label: "Estimated Complexity"
      description: "Agent task complexity level"
      options:
        - Low (< 4 hours)
        - Medium (4-8 hours)
        - High (8+ hours, consider splitting)
    validations:
      required: true

  - type: textarea
    id: context
    attributes:
      label: "Background Context"
      description: "Brief context about why this task is needed"
      placeholder: "This task is part of the MVP upload flow..."
    validations:
      required: true

  - type: textarea
    id: acceptance_criteria
    attributes:
      label: "Acceptance Criteria"
      description: "Specific, testable outcomes (use checkboxes)"
      placeholder: |
        - [ ] API endpoint `/upload` accepts multipart form data
        - [ ] Returns SAS URL for blob storage
        - [ ] Validates file size < 50MB
        - [ ] Returns appropriate error codes
      render: markdown
    validations:
      required: true

  - type: textarea
    id: implementation_guide
    attributes:
      label: "Implementation Guide"
      description: "Specific guidance for the agent"
      placeholder: |
        **Code patterns to follow:** `.ai/rules/api-design-standards.md`
        **Reference implementations:** `.ai/references/azure-blob-patterns.md`
        **Dependencies:** Azure Storage SDK, FastAPI
        **File locations:** `src/api/upload.py`
    validations:
      required: true

  - type: textarea
    id: test_requirements
    attributes:
      label: "Test Requirements"
      description: "Specific tests that must be implemented"
      placeholder: |
        - Unit tests for upload validation
        - Integration test with Azure Blob Storage
        - Error handling tests (oversized files, invalid formats)
    validations:
      required: true

  - type: input
    id: dependencies
    attributes:
      label: "Task Dependencies"
      description: "Other issues that must be completed first (use #issue_number)"
      placeholder: "#123, #456"

  - type: checkboxes
    id: definition_of_done
    attributes:
      label: "Definition of Done"
      description: "Standard checklist for all agent tasks"
      options:
        - label: "Code implemented according to acceptance criteria"
          required: false
        - label: "All tests passing (unit and integration)"
          required: false
        - label: "Code follows project standards (linting, formatting)"
          required: false
        - label: "Documentation updated (if applicable)"
          required: false
        - label: "PR created using proper template"
          required: false
        - label: "No breaking changes or dependencies resolved"
          required: false

  - type: input
    id: branch_name
    attributes:
      label: "Suggested Branch Name"
      description: "Following feat/X.X-slug convention"
      placeholder: "feat/1.1-upload-api"

  - type: textarea
    id: agent_notes
    attributes:
      label: "Notes for AI Agent"
      description: "Any specific instructions or constraints for the agent"
      placeholder: |
        - Use async/await patterns for all Azure SDK calls
        - Include comprehensive error logging
        - Follow the existing FastAPI dependency injection pattern 