---
phase: "1.4"
classification: "agent-ready"
complexity: "medium"
estimated_hours: 5
dependencies: ["#1.2", "#1.3"]
assignee: "devin-ai"
created: 2024-01-15
---

# 🤖 Phase 1.4: Pipeline Endpoint Trigger Function

## Agent Assignment: READY
**Estimated Complexity:** Medium (5 hours)
**Dependencies:** Blob Trigger Function (#1.2), AML Endpoint (#1.3)

## Background Context
This Azure Function processes messages from the Service Bus queue created by the blob trigger function. It calls the deployed AML managed online endpoint with the PDF processing parameters, handles the response, and manages job status tracking. This decouples the trigger logic from the actual pipeline execution.

## Acceptance Criteria
- [ ] Azure Function with Service Bus queue trigger
- [ ] HTTP client to call AML managed online endpoint
- [ ] Job status tracking in Azure Table Storage
- [ ] Error handling with retry logic and dead letter queue
- [ ] Correlation ID propagation for request tracing
- [ ] Timeout handling for long-running pipeline jobs
- [ ] Status update notifications (optional webhook support)
- [ ] Monitoring and alerting for failed jobs

## Implementation Guide
**Code patterns to follow:** `.ai/rules/azure-function-standards.md`
**Reference implementations:** `.ai/references/aml-endpoint-client-patterns.md`
**Dependencies:** Azure Functions SDK, Azure ML SDK, requests, azure-data-tables
**File locations:** 
- `functions/pipeline_trigger/__init__.py`
- `functions/pipeline_trigger/function.json`
- `shared/aml_client.py`
- `shared/status_tracker.py`

## Test Requirements
- Unit tests for AML endpoint client
- Integration test with Service Bus queue
- Error handling tests (endpoint timeouts, failures)
- Status tracking validation tests
- Retry logic and dead letter queue tests
- Performance tests (concurrent message processing)

## Definition of Done
- [ ] Function deployed and processing queue messages
- [ ] All tests passing (unit and integration)
- [ ] Job status tracking working correctly
- [ ] Error handling covers all scenarios
- [ ] Monitoring dashboards configured
- [ ] Performance benchmarks met (< 5s message processing)

## Technical Specifications
```python
# Job Status Table Schema
{
    "PartitionKey": "jobs",
    "RowKey": "document_id",
    "job_id": "aml_job_uuid",
    "status": "queued|running|completed|failed",
    "created_timestamp": "2024-01-15T10:30:00Z",
    "started_timestamp": "2024-01-15T10:31:00Z",
    "completed_timestamp": "2024-01-15T10:45:00Z",
    "error_message": null,
    "result_locations": {
        "markdown": "blob_uri",
        "embeddings": "blob_uri", 
        "metadata": "blob_uri"
    },
    "processing_time_seconds": 840,
    "correlation_id": "trace_uuid"
}
```

**Function Configuration:**
```json
{
    "bindings": [
        {
            "name": "msg",
            "type": "serviceBusTrigger",
            "direction": "in",
            "queueName": "pipeline-processing-queue",
            "connection": "ServiceBusConnection"
        }
    ],
    "scriptFile": "__init__.py"
}
```

**Environment Variables:**
- `AML_ENDPOINT_URL`
- `AML_ENDPOINT_KEY`
- `SERVICEBUS_CONNECTION_STRING`
- `STORAGE_CONNECTION_STRING`
- `STATUS_TABLE_NAME=job_status`

## Notes for AI Agent
- Use Azure Functions Python v2 programming model
- Implement exponential backoff for AML endpoint calls
- Handle AML endpoint authentication using API keys or managed identity
- Parse and validate Service Bus message format from blob trigger
- Update job status at each stage (queued → running → completed/failed)
- Include comprehensive error logging with correlation IDs
- Set appropriate function timeout (20 minutes to handle long pipelines)
- Implement circuit breaker pattern for endpoint failures
- Use structured logging for monitoring and debugging

## Error Handling Strategy
- **Transient errors**: Retry with exponential backoff (max 3 attempts)
- **Authentication errors**: Log and move to dead letter queue
- **Timeout errors**: Mark job as failed, notify user
- **Endpoint unavailable**: Retry with circuit breaker logic
- **Invalid message format**: Log error and discard message

## Status Update Flow
1. Receive message from queue → Update status to "queued"
2. Call AML endpoint → Update status to "running" 
3. Poll endpoint for completion → Update with progress
4. Endpoint returns results → Update status to "completed"
5. Any error occurs → Update status to "failed" with error details 