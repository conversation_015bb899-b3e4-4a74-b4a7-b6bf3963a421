'use client';

import { useState } from 'react';

interface V0GeneratorProps {
  className?: string;
}

export function V0Generator({ className }: V0GeneratorProps) {
  const [prompt, setPrompt] = useState('');
  const [generatedCode, setGeneratedCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedModel, setSelectedModel] = useState<'v0-1.5-md' | 'v0-1.5-lg' | 'v0-1.0-md'>(
    'v0-1.5-md',
  );

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      setError('Please enter a prompt');
      return;
    }

    setIsLoading(true);
    setError(null);
    setGeneratedCode('');

    try {
      const response = await fetch('/api/v0/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          model: selectedModel,
          stream: false,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate code');
      }

      setGeneratedCode(data.text);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(generatedCode);
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  return (
    <div className={`max-w-4xl mx-auto p-6 space-y-6 ${className}`}>
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">v0 Component Generator</h2>
        <p className="text-gray-600">
          Generate React components using v0&apos;s AI models. Describe what you want to build and
          get production-ready code.
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label htmlFor="model-select" className="block text-sm font-medium mb-2">
            Select Model
          </label>
          <select
            id="model-select"
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value as 'v0-1.5-md' | 'v0-1.5-lg' | 'v0-1.0-md')}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="v0-1.5-md">v0-1.5-md (Everyday tasks & UI generation)</option>
            <option value="v0-1.5-lg">v0-1.5-lg (Advanced thinking & reasoning)</option>
            <option value="v0-1.0-md">v0-1.0-md (Legacy model)</option>
          </select>
        </div>

        <div>
          <label htmlFor="prompt" className="block text-sm font-medium mb-2">
            Describe your component
          </label>
          <textarea
            id="prompt"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="e.g., Create a modern pricing card component with three tiers..."
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-h-[100px] resize-y"
            disabled={isLoading}
          />
        </div>

        <button
          onClick={handleGenerate}
          disabled={isLoading || !prompt.trim()}
          className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          {isLoading ? 'Generating...' : 'Generate Component'}
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="text-red-800">
              <strong>Error:</strong> {error}
            </div>
          </div>
        </div>
      )}

      {generatedCode && (
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Generated Component</h3>
            <button
              onClick={handleCopy}
              className="bg-gray-100 hover:bg-gray-200 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              Copy Code
            </button>
          </div>
          <pre className="bg-gray-50 border rounded-lg p-4 overflow-x-auto">
            <code className="text-sm">{generatedCode}</code>
          </pre>
        </div>
      )}
    </div>
  );
}
