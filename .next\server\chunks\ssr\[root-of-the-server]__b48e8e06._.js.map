{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_edb9147b.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_edb9147b-module__xWQRDW__className\",\n  \"variable\": \"inter_edb9147b-module__xWQRDW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_edb9147b.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-sans%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Repos/studyscore-website/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from 'next';\nimport { Inter } from 'next/font/google';\nimport './globals.css';\n\nconst inter = Inter({\n  subsets: ['latin'],\n  variable: '--font-sans',\n});\n\nexport const metadata: Metadata = {\n  title: 'StudyScore',\n  description: 'Sports Science Research Knowledge Graph Platform',\n  keywords: ['research', 'sports science', 'knowledge graph', 'papers'],\n  authors: [\n    {\n      name: 'StudyScore Team',\n    },\n  ],\n  creator: 'StudyScore',\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" suppressHydrationWarning>\n      <body className={`min-h-screen bg-background font-sans antialiased ${inter.variable}`}>\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AASO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAY;QAAkB;QAAmB;KAAS;IACrE,SAAS;QACP;YACE,MAAM;QACR;KACD;IACD,SAAS;AACX;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,wBAAwB;kBACtC,cAAA,8OAAC;YAAK,WAAW,CAAC,iDAAiD,EAAE,yIAAA,CAAA,UAAK,CAAC,QAAQ,EAAE;sBAClF;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Repos/studyscore-website/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}