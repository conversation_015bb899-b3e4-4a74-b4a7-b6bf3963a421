import { Vercel } from '@vercel/sdk';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function deployStudyScore() {
  try {
    // Initialize Vercel client
    const vercel = new Vercel({
      bearerToken: process.env.VERCEL_TOKEN,
    });

    console.log('🚀 Starting deployment of StudyScore v0...');

    // Create deployment from GitHub repository
    const deploymentRequest = {
      name: 'studyscore-v0',
      gitSource: {
        type: 'github',
        repo: 'chrisathlea/studyscore-v0',
        ref: 'main',
      },
      target: 'production',
      projectSettings: {
        framework: 'vite',
        buildCommand: 'npm run build',
        outputDirectory: 'dist',
        installCommand: 'npm install',
      },
    };

    console.log('📋 Deployment request:', JSON.stringify(deploymentRequest, null, 2));

    const deployment = await vercel.deployments.createDeployment({
      requestBody: deploymentRequest,
    });

    console.log('✅ Deployment created:', deployment);
    console.log('📦 Deployment ID:', deployment.uid);
    console.log('🔗 Preview URL:', deployment.url);

    // Wait for deployment to complete
    console.log('⏳ Waiting for deployment to complete...');

    let status = 'BUILDING';
    let attempts = 0;
    const maxAttempts = 60; // 5 minutes max

    while ((status === 'BUILDING' || status === 'QUEUED') && attempts < maxAttempts) {
      await new Promise((resolve) => setTimeout(resolve, 5000)); // Wait 5 seconds

      try {
        const deploymentStatus = await vercel.deployments.getDeployment({
          idOrUrl: deployment.uid,
        });

        status = deploymentStatus.readyState;
        console.log(`📊 Status: ${status} (${attempts + 1}/${maxAttempts})`);
        attempts++;
      } catch (error) {
        console.log('⚠️ Error checking status:', error.message);
        attempts++;
      }
    }

    if (status === 'READY') {
      console.log('🎉 Deployment successful!');
      console.log(`🌐 Live URL: https://${deployment.url}`);
    } else if (attempts >= maxAttempts) {
      console.log('⏰ Deployment timeout - check Vercel dashboard for status');
    } else {
      console.log('❌ Deployment failed with status:', status);
    }
  } catch (error) {
    console.error('❌ Deployment error:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
    console.error('Full error:', error);
  }
}

// Run the deployment
deployStudyScore();
