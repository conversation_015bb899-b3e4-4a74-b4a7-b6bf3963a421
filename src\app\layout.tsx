import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
});

export const metadata: Metadata = {
  title: 'StudyScore',
  description: 'Sports Science Research Knowledge Graph Platform',
  keywords: ['research', 'sports science', 'knowledge graph', 'papers'],
  authors: [
    {
      name: 'StudyScore Team',
    },
  ],
  creator: 'StudyScore',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`min-h-screen bg-background font-sans antialiased ${inter.variable}`}>
        {children}
      </body>
    </html>
  );
}
