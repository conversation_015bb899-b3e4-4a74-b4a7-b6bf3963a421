# v0 API Integration Setup

This project includes integration with v0's latest API for AI-powered React component generation.

## Prerequisites

1. **v0 API Access**: The v0 API is currently in beta and requires:
   - A Premium or Team plan with usage-based billing enabled
   - An API key from [v0.dev](https://v0.dev)

2. **Supported Models**:
   - `v0-1.5-md`: For everyday tasks and UI generation
   - `v0-1.5-lg`: For advanced thinking or reasoning  
   - `v0-1.0-md`: Legacy model

## Setup Instructions

### 1. Get Your v0 API Key

1. Visit [v0.dev](https://v0.dev)
2. Sign up or log in to your account
3. Navigate to API settings and create a new API key
4. Copy your API key

### 2. Configure Environment Variables

Update your `.env` file in the project root:

```env
# v0 API Configuration
V0_API_KEY=your_actual_v0_api_key_here
```

⚠️ **Important**: Never commit your actual API key to version control.

### 3. Install Dependencies

The required dependencies are already installed:
- `ai` - AI SDK for TypeScript
- `@ai-sdk/vercel` - Vercel provider for AI SDK

### 4. Test the Integration

1. Start your development server:
```bash
npm run dev
```

2. Visit the demo page: `http://localhost:3000/v0-demo`

3. Test the API health check: `http://localhost:3000/api/v0/generate`

## Usage Examples

### Basic Text Generation

```typescript
import { generateV0Text } from '@/lib/v0-client';

const result = await generateV0Text({
  prompt: 'Create a modern button component',
  model: 'v0-1.5-md'
});

console.log(result.text);
```

### UI Component Generation

```typescript
import { generateV0UI } from '@/lib/v0-client';

const result = await generateV0UI({
  prompt: 'Create a responsive pricing card with three tiers',
  model: 'v0-1.5-md'
});

console.log(result.text);
```

### Streaming Responses

```typescript
import { streamV0Text } from '@/lib/v0-client';

const stream = await streamV0Text({
  prompt: 'Create a dashboard layout component',
  model: 'v0-1.5-md'
});

for await (const chunk of stream.textStream) {
  console.log(chunk);
}
```

## API Reference

### Available Functions

- `generateV0Text()`: Generate text using v0 API
- `generateV0Object()`: Generate structured objects with schema validation
- `streamV0Text()`: Stream text responses
- `generateV0UI()`: Generate UI components with specialized prompting

### API Endpoint

- `POST /api/v0/generate`: Generate content with v0
- `GET /api/v0/generate`: Health check and configuration info

### Request Format

```json
{
  "prompt": "Your generation prompt",
  "model": "v0-1.5-md",
  "stream": false
}
```

### Response Format

```json
{
  "text": "Generated content...",
  "usage": {
    "promptTokens": 50,
    "completionTokens": 200,
    "totalTokens": 250
  }
}
```

## Usage Limits

| Model     | Max Context Window | Max Output Context |
|-----------|-------------------|-------------------|
| v0-1.5-md | 128,000 tokens    | 32,000 tokens     |
| v0-1.5-lg | 512,000 tokens    | 32,000 tokens     |
| v0-1.0-md | 128,000 tokens    | 32,000 tokens     |

## Error Handling

The integration includes comprehensive error handling:

- **Missing API Key**: Clear error message when `V0_API_KEY` is not set
- **Rate Limiting**: Automatic retry logic for rate-limited requests
- **Network Errors**: Graceful handling of network issues
- **Invalid Requests**: Validation of input parameters

## Best Practices

1. **Prompt Engineering**: Be specific and clear in your prompts
2. **Model Selection**: Use `v0-1.5-md` for most UI generation tasks
3. **Error Handling**: Always wrap API calls in try-catch blocks
4. **Rate Limiting**: Implement client-side rate limiting for user interfaces
5. **Caching**: Consider caching frequently generated components

## Troubleshooting

### Common Issues

1. **"V0_API_KEY environment variable is required"**
   - Ensure your `.env` file contains the correct API key
   - Restart your development server after updating environment variables

2. **Network or API errors**
   - Check your internet connection
   - Verify your API key is still valid
   - Check v0's status page for service issues

3. **High token usage**
   - Review your prompts for unnecessary complexity
   - Consider using shorter, more focused prompts
   - Monitor usage in your v0 dashboard

## Resources

- [v0 API Documentation](https://vercel.com/docs/v0/api)
- [AI SDK Documentation](https://sdk.vercel.ai/)
- [v0.dev](https://v0.dev)

## Support

For API-related issues, contact v0 <NAME_EMAIL> 