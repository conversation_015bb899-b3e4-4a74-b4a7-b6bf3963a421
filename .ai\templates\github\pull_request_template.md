# Pull Request Template

## 📋 Task Information
**Related Issue:** Closes #[issue_number]
**Agent Classification:** 🤖 Agent-Ready | 👥 Agent-Assisted | 👨‍💻 Human-Only
**Roadmap Phase:** [e.g., 1.1, 2.3]
**Sprint:** [e.g., sprint-1-mvp-upload]

## 📝 Description
Brief description of what this PR accomplishes and why it's needed.

### Changes Made
- [ ] List the main changes
- [ ] Include any architectural decisions
- [ ] Note any breaking changes

## 🧪 Testing
### Test Coverage
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated  
- [ ] End-to-end tests added/updated
- [ ] All tests passing locally

### Manual Testing
- [ ] Tested happy path scenarios
- [ ] Tested error conditions
- [ ] Tested edge cases
- [ ] Performance impact assessed

## 📖 Documentation
- [ ] Code comments added where needed
- [ ] README updated (if applicable)
- [ ] API documentation updated (if applicable)
- [ ] Architecture docs updated (if applicable)

## 🔄 Code Quality
- [ ] Code follows project standards
- [ ] Linting passes (`pylint`, `black`, `isort`)
- [ ] Type hints added where appropriate
- [ ] No security vulnerabilities introduced
- [ ] No performance regressions

## 🔗 Dependencies
- [ ] No new dependencies added
- [ ] OR: New dependencies justified and approved
- [ ] Dependencies updated in requirements.txt
- [ ] Lock files updated

## 🚀 Deployment
- [ ] No breaking changes
- [ ] Database migrations included (if needed)
- [ ] Environment variables documented (if new)
- [ ] Azure resources configured (if needed)

## 🤖 AI Agent Checklist (if applicable)
- [ ] All acceptance criteria from issue met
- [ ] Implementation follows specified patterns
- [ ] Required tests implemented
- [ ] No architectural decisions made without human review

## 📸 Screenshots/Demos (if applicable)
<!-- Include screenshots for UI changes or GIFs for flow changes -->

## 🔍 Review Notes
### Areas for Special Attention
<!-- Highlight areas where you want extra review focus -->

### Questions for Reviewers
<!-- Any specific questions or concerns -->

## 🚦 Ready for Review Checklist
- [ ] PR title follows convention: `[Phase X.X] Brief Description`
- [ ] All checklist items completed
- [ ] Self-review completed
- [ ] Ready for team review

---

**Reviewer Guidelines:**
- For 🤖 Agent-Ready tasks: Focus on correctness and test coverage
- For 👥 Agent-Assisted tasks: Review architectural decisions and patterns
- For 👨‍💻 Human-Only tasks: Comprehensive review of design and implementation 