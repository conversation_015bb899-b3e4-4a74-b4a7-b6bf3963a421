---
phase: "1.6"
classification: "agent-ready"
complexity: "medium"
estimated_hours: 7
dependencies: ["#1.3"]
assignee: "devin-ai"
created: 2024-01-15
---

# 🤖 Phase 1.6: Mini Causal Report Generator

## Agent Assignment: READY
**Estimated Complexity:** Medium (7 hours)
**Dependencies:** AML Pipeline Endpoint (#1.3)

## Background Context
Generate a concise causality analysis report from processed document chunks using LLM analysis. This component queries the top-k most relevant chunks from Azure Cognitive Search, analyzes them for causal relationships, and produces a 300-word summary highlighting key interventions, outcomes, and confidence levels. This differentiates our service from basic OCR by providing domain-specific insights.

## Acceptance Criteria
- [ ] Query Azure Cognitive Search for top-k causal chunks
- [ ] LLM prompt engineering for causality analysis
- [ ] Generate structured 300-word causal summary
- [ ] Extract confidence scores and relationship types
- [ ] Handle documents with no clear causal relationships
- [ ] Include methodology and limitations section
- [ ] Save report as both TXT and JSON formats
- [ ] Integration with result packaging function

## Implementation Guide
**Code patterns to follow:** `.ai/rules/llm-integration-standards.md`
**Reference implementations:** `.ai/references/azure-openai-patterns.md`
**Dependencies:** Azure OpenAI SDK, azure-search-documents, tiktoken
**File locations:** 
- `src/analysis/causal_analyzer.py`
- `src/analysis/prompts/causality_analysis.txt`
- `src/analysis/report_formatter.py`
- `tests/test_causal_analysis.py`

## Test Requirements
- Unit tests for chunk selection and ranking
- LLM prompt validation tests
- Report format and structure tests
- Error handling tests (no causal content, API failures)
- Performance tests (token usage optimization)
- Integration tests with ACS and OpenAI

## Definition of Done
- [ ] Causal analysis component implemented and tested
- [ ] Report generation working for various document types
- [ ] All tests passing (unit and integration)
- [ ] Token usage optimized (< 4000 tokens per analysis)
- [ ] Error handling covers edge cases
- [ ] Documentation and examples provided

## Technical Specifications
```python
# Causal Report JSON Structure
{
    "document_id": "uuid4",
    "analysis_timestamp": "2024-01-15T10:45:00Z",
    "methodology": "LLM-based causal relationship extraction",
    "summary": "300-word narrative summary",
    "key_findings": [
        {
            "intervention": "High-intensity interval training",
            "outcome": "Reduced ACL injury risk",
            "confidence": 0.85,
            "evidence_strength": "moderate",
            "chunk_references": ["chunk_12", "chunk_18", "chunk_24"]
        }
    ],
    "causal_relationships": [
        {
            "cause": "Plyometric training",
            "effect": "Improved neuromuscular control", 
            "relationship_type": "positive_correlation",
            "confidence": 0.78,
            "supporting_evidence": "3 studies, n=245 participants"
        }
    ],
    "limitations": [
        "Analysis based on document content only",
        "Causal inference limited by study design",
        "Confidence scores are estimates"
    ],
    "token_usage": {
        "input_tokens": 2400,
        "output_tokens": 800,
        "total_cost_usd": 0.048
    }
}
```

**LLM Prompt Template:**
```
You are a scientific research analyst specializing in causality analysis. 
Analyze the following research document chunks for causal relationships.

DOCUMENT CHUNKS:
{top_k_chunks}

ANALYSIS REQUIREMENTS:
1. Identify clear causal relationships (cause → effect)
2. Assess confidence levels (0.0-1.0) based on evidence strength
3. Distinguish between correlation and causation
4. Note study design limitations
5. Provide 300-word summary focusing on actionable insights

OUTPUT FORMAT: JSON with summary, key_findings, causal_relationships, limitations
```

**Environment Variables:**
- `AZURE_OPENAI_ENDPOINT`
- `AZURE_OPENAI_API_KEY`
- `AZURE_OPENAI_DEPLOYMENT_NAME`
- `ACS_ENDPOINT`
- `ACS_API_KEY`
- `MAX_CHUNKS_FOR_ANALYSIS=10`

## Notes for AI Agent
- Use GPT-4 for better causal reasoning capabilities
- Implement token counting to stay within limits
- Use semantic search to find chunks with causal keywords
- Include confidence calibration based on study design indicators
- Handle edge cases (no causal content, contradictory findings)
- Implement caching for repeated analyses of same document
- Use structured output parsing to ensure consistent JSON format
- Include cost tracking and optimization

## Chunk Selection Strategy
1. **Semantic search** for causal keywords: "caused by", "resulted in", "led to", "intervention", "outcome"
2. **Score chunks** by causal relevance using custom scoring function
3. **Select top-k chunks** (default k=10) with diversity to avoid redundancy
4. **Validate chunk quality** (minimum length, coherence check)
5. **Combine chunks** with context preservation for LLM analysis

## Quality Assurance
- **Confidence calibration**: Compare LLM confidence with human expert ratings
- **Consistency checks**: Multiple runs should produce similar core findings
- **Bias detection**: Check for systematic biases in causal attribution
- **Evidence validation**: Cross-reference findings with chunk content
- **Output validation**: Ensure JSON structure and required fields 