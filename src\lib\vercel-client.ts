import { Vercel } from '@vercel/sdk';

if (!process.env.VERCEL_TOKEN) {
  throw new Error('VERCEL_TOKEN environment variable is not set');
}

export const vercel = new Vercel({
  bearerToken: process.env.VERCEL_TOKEN,
});

/**
 * Checks the Vercel API connection by fetching the authenticated user's info.
 */
export async function checkVercelConnection() {
  try {
    const { user } = await vercel.user.getAuthUser();
    return { success: true, user };
  } catch (error: any) {
    console.error('Vercel API connection error:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Fetches deployments for a given project ID.
 */
export async function getVercelDeployments(projectId: string) {
    if (!process.env.VERCEL_ORG_ID) {
        throw new Error('VERCEL_ORG_ID environment variable is not set');
    }
    try {
        const { data: deployments } = await vercel.deployments.list({
            projectId: projectId,
            teamId: process.env.VERCEL_ORG_ID,
        });
        return { success: true, deployments };
    } catch (error: any)
    {
        console.error('Failed to fetch Vercel deployments:', error);
        return { success: false, error: error.message };
    }
} 