import { createMcpHandler } from '@vercel/mcp-adapter';
import { z } from 'zod';

const handler = createMcpHandler((server) => {
  // Expose a "roll_dice" tool
  server.tool(
    'roll_dice',
    'Rolls an N-sided die and returns the result.',
    {
      sides: z.number().int().min(2).describe('The number of sides on the die'),
    },
    async ({ sides }: { sides: number }) => {
      const value = 1 + Math.floor(Math.random() * sides);
      console.log(`Rolled a d${sides}, got: ${value}`);
      return {
        content: [{ type: 'text', text: `🎲 You rolled a ${value}!` }],
      };
    }
  );

  // You can add more tools here
  // server.tool(...)
});

export { handler as GET, handler as POST }; 