# AI-Assisted Development Organization System

This folder contains the organizational structure for AI-assisted development of DocStream-Cloud.

## Folder Structure

```
.ai/
├── tasks/              # Kanban-style task management
│   ├── backlog/        # Future tasks, not ready to start
│   ├── doing/          # Currently active tasks
│   └── done/           # Completed work, organized by sprints
├── rules/              # Development standards and patterns
├── references/         # Learning materials and architecture docs
├── llms/               # AI context and prompts
└── templates/          # GitHub templates and automation
```

## GitHub Integration

- **Synced with GitHub Projects**: Tasks automatically sync with our project board
- **Issue Templates**: Standardized issue creation with proper labeling
- **PR Templates**: Consistent pull request format with checklists
- **Automation**: GitHub Actions trigger based on task folder changes

## Remote Agent Triaging

Tasks are classified into three categories:
- 🤖 **AGENT_READY**: Fully autonomous AI agent tasks
- 👥 **AGENT_ASSISTED**: Human-led with AI assistance
- 👨‍💻 **HUMAN_ONLY**: Complex tasks requiring human judgment

## Sprint Organization

- Max 10 tasks per sprint
- Mix of agent-ready and human tasks
- Clear dependencies and acceptance criteria
- Aligned with DocStream-Cloud roadmap phases 

# Task Management System

## Sprint Organization Strategy

### Sprint Naming Convention
- `sprint-X-<phase>-<focus>` (e.g., `sprint-1-mvp-upload`, `sprint-2-enterprise-auth`)
- Max 10 tasks per sprint
- 1-2 week duration recommended

### Task Classification & Triaging

#### 🤖 AGENT_READY Tasks
**Criteria:**
- Well-defined scope and acceptance criteria
- Clear input/output specifications
- Minimal architectural decisions required
- Existing patterns to follow
- Can be tested programmatically

**Examples:**
- API endpoint creation from OpenAPI spec
- Database schema implementation
- Unit test creation
- Configuration file setup
- Documentation updates

**Template:**
```markdown
# 🤖 [Task Title]

## Agent Assignment: READY
**Estimated Complexity:** Low/Medium
**Dependencies:** [List any blocking tasks]

## Acceptance Criteria
- [ ] Specific, testable outcome 1
- [ ] Specific, testable outcome 2

## Implementation Guide
- Code patterns to follow: [Link to rules/]
- Reference implementations: [Link to references/]
- Tests required: [Specific test cases]

## Definition of Done
- [ ] Code implemented
- [ ] Tests passing
- [ ] Documentation updated
- [ ] PR created with proper template
```

#### 👥 AGENT_ASSISTED Tasks
**Criteria:**
- Requires some architectural decisions
- Complex business logic
- Integration between multiple systems
- Performance optimization needed

**Examples:**
- Complex API design
- Pipeline optimization
- Multi-service integration
- Error handling strategies

#### 👨‍💻 HUMAN_ONLY Tasks
**Criteria:**
- Strategic decisions required
- User experience design
- Security architecture
- Performance bottleneck analysis
- Vendor evaluations

**Examples:**
- System architecture design
- Security review
- Performance analysis
- User research
- Technology stack decisions

## Sprint Planning Process

### 1. Backlog Grooming
- Review `backlog/` tasks weekly
- Ensure tasks have proper classification
- Update dependencies
- Estimate complexity

### 2. Sprint Planning
- Select mix of task types (aim for 60% agent-ready, 30% agent-assisted, 10% human-only)
- Verify all dependencies are met
- Assign to team members or agents
- Create GitHub issues automatically

### 3. Sprint Execution
- Move tasks from `backlog/` → `doing/` → `done/sprint-X/`
- Daily standups review progress
- Blockers escalated immediately

## GitHub Integration Commands

### Create Sprint from Tasks
```bash
# Run from repo root
.ai/scripts/create-sprint.sh sprint-1-mvp-upload
```

### Sync with GitHub Projects
```bash
# Auto-sync tasks to GitHub Projects board
.ai/scripts/sync-github-projects.sh
```

## File Naming Conventions

### Backlog Tasks
- `[phase].[task]-[slug].md` (e.g., `1.1-upload-ui.md`)
- Include classification emoji in filename

### Active Tasks
- Move to `doing/` with same filename
- Add assignee info

### Completed Tasks
- Move to `done/sprint-X/` 
- Include completion date and outcomes 