{"$schema": "https://biomejs.dev/schemas/1.4.1/schema.json", "files": {"ignore": [".next/**", "node_modules/**", "dist/**", "build/**"]}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": false, "correctness": {"noUnusedVariables": "warn"}, "suspicious": {"noExplicitAny": "warn"}, "style": {"noNonNullAssertion": "warn"}}}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100}, "javascript": {"formatter": {"quoteStyle": "single", "trailingCommas": "all", "semicolons": "always"}}}