import { NextResponse } from 'next/server';
import { checkVercelConnection } from '@/lib/vercel-client';

export const dynamic = 'force-dynamic'; // Ensures the route is not cached

export async function GET() {
  const result = await checkVercelConnection();

  if (result.success) {
    return NextResponse.json({
      status: 'Vercel API connection successful',
      user: {
        id: result.user?.id,
        email: result.user?.email,
        username: result.user?.username,
      },
      sdkInstalled: true,
    });
  } else {
    return NextResponse.json(
      {
        status: 'Vercel API connection failed',
        error: result.error,
      },
      { status: 500 }
    );
  }
} 