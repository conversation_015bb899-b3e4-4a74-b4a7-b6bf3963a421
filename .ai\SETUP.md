# AI-Assisted Development Setup Guide

This guide will help you implement the complete AI-assisted development system for DocStream-Cloud, including task management, GitHub integration, and remote agent workflows.

## 🚀 Quick Start (5 Minutes)

1. **Verify Folder Structure**
   ```bash
   # Check that all folders exist
   ls -la .ai/
   # Should show: tasks/, rules/, references/, llms/, templates/, scripts/
   ```

2. **Set Environment Variables**
   ```bash
   # Add to your .env file
   echo "GITHUB_TOKEN=your_github_token_here" >> .env
   echo "GITHUB_OWNER=your-username" >> .env
   echo "GITHUB_REPO=markdown-mill-local" >> .env
   ```

3. **Test Task Sync**
   ```bash
   python .ai/scripts/sync-github-projects.py
   ```

## 📋 GitHub Integration Setup

### 1. Enable GitHub Projects (v2)
- Go to your repository → Projects tab
- Create new project: "DocStream-Cloud Development"
- Add custom fields:
  - `Agent Classification` (Single select: Agent-Ready, Agent-Assisted, Human-Only)
  - `Phase` (Text: 1.1, 1.2, etc.)
  - `Complexity` (Single select: Low, Medium, High)
  - `Sprint` (Text: sprint-1-mvp-upload, etc.)

### 2. Configure Repository Settings
```bash
# Enable GitHub Actions
# Go to Settings → Actions → General
# Set "Actions permissions" to "Allow all actions and reusable workflows"

# Set up branch protection
# Go to Settings → Branches → Add rule
# Branch name pattern: main
# ✅ Require pull request reviews before merging
# ✅ Require status checks to pass before merging
```

### 3. Add GitHub Secrets
Navigate to Settings → Secrets and variables → Actions:
- `GITHUB_TOKEN`: Your personal access token with repo, issues, and project permissions

### 4. Install Issue Templates
The templates are already created in `.ai/templates/github/`. Copy them:
```bash
mkdir -p .github/ISSUE_TEMPLATE
cp .ai/templates/github/ISSUE_TEMPLATE/* .github/ISSUE_TEMPLATE/
cp .ai/templates/github/pull_request_template.md .github/
```

## 🤖 Remote Agent Configuration

### For Devin AI
1. **Grant Repository Access**
   - Add Devin to your repository as a collaborator
   - Ensure it has "Write" permissions

2. **Agent Workflow**
   ```bash
   # 1. Assign agent-ready task
   # Move task from backlog/ to doing/
   mv .ai/tasks/backlog/1.1-upload-ui.md .ai/tasks/doing/

   # 2. Agent creates branch
   # Agent should follow naming: feat/1.1-upload-ui

   # 3. Agent implements and creates PR
   # PR will auto-trigger validation workflows
   ```

### For Other AI Agents
1. **API Access Pattern**
   ```python
   # Example for programmatic task assignment
   import requests
   
   def assign_task_to_agent(task_file, agent_name):
       # Read task file
       with open(f".ai/tasks/backlog/{task_file}", 'r') as f:
           task_content = f.read()
       
       # Create GitHub issue
       issue_data = {
           "title": f"🤖 {task_file}",
           "body": task_content,
           "labels": ["agent-ready"],
           "assignee": agent_name
       }
       
       response = requests.post(
           f"https://api.github.com/repos/{owner}/{repo}/issues",
           headers={"Authorization": f"token {github_token}"},
           json=issue_data
       )
       
       return response.json()
   ```

## 📁 Task Management Workflow

### 1. Creating New Tasks
```bash
# Create new task file
cp .ai/tasks/backlog/1.1-upload-ui.md .ai/tasks/backlog/1.2-new-task.md

# Edit the file with:
# - Proper front matter (phase, classification, etc.)
# - Clear acceptance criteria
# - Implementation guide
# - Test requirements
```

### 2. Sprint Planning Process
```bash
# Weekly sprint planning
.ai/scripts/create-sprint.sh sprint-2-enterprise-auth

# This creates:
# - .ai/tasks/done/sprint-2-enterprise-auth/
# - Moves selected tasks from backlog to doing
# - Creates GitHub milestone
# - Updates project board
```

### 3. Task Progression
```
backlog/ → doing/ → done/sprint-X/
   ↓         ↓         ↓
Planned  → Active  → Complete
```

## 🔧 Customization Guide

### 1. Adding New Rules
```bash
# Create rule file
cat > .ai/rules/api-design-standards.md << 'EOF'
# API Design Standards

## REST Endpoint Patterns
- Use nouns for resources: `/api/v1/documents` not `/api/v1/get-documents`
- Use HTTP methods correctly: GET, POST, PUT, DELETE
- Version your APIs: `/api/v1/`, `/api/v2/`

## Error Handling
- Always return consistent error format
- Use appropriate HTTP status codes
- Include error codes for programmatic handling
EOF
```

### 2. Adding Reference Materials
```bash
# Create reference document
cat > .ai/references/azure-blob-upload-patterns.md << 'EOF'
# Azure Blob Upload Patterns

## SAS Token Generation
```python
from azure.storage.blob import generate_blob_sas, BlobSasPermissions

def generate_upload_sas(container_name: str, blob_name: str):
    sas_token = generate_blob_sas(
        account_name="your_account",
        container_name=container_name,
        blob_name=blob_name,
        account_key="your_key",
        permission=BlobSasPermissions(write=True),
        expiry=datetime.utcnow() + timedelta(hours=1)
    )
    return sas_token
```
EOF
```

### 3. Custom Agent Classifications
Edit `.ai/tasks/README.md` to add new classifications:
```markdown
#### 🔄 AGENT_REVIEW
**Criteria:**
- Generated by AI but requires human review
- Complex logic that needs validation
- Security-sensitive components

**Examples:**
- Authentication middleware
- Data validation rules
- Performance-critical code
```

## 📊 Monitoring and Analytics

### 1. Task Metrics Dashboard
The GitHub Actions workflow provides automatic health checks:
- Sprint velocity (tasks completed per week)
- Agent vs human task ratio
- Blocked task identification
- Classification accuracy

### 2. Custom Metrics Collection
```python
# Add to .ai/scripts/collect-metrics.py
def analyze_sprint_velocity():
    """Calculate tasks completed per sprint."""
    completed_sprints = list(Path('.ai/tasks/done').iterdir())
    
    metrics = {}
    for sprint_dir in completed_sprints:
        task_count = len(list(sprint_dir.glob('*.md')))
        metrics[sprint_dir.name] = task_count
    
    return metrics
```

## 🔍 Troubleshooting

### Common Issues

1. **Tasks Not Syncing to GitHub**
   ```bash
   # Check GitHub token permissions
   curl -H "Authorization: token $GITHUB_TOKEN" \
        https://api.github.com/user
   
   # Verify repository access
   python .ai/scripts/sync-github-projects.py --dry-run
   ```

2. **Agent Not Picking Up Tasks**
   - Verify task has proper `agent-ready` classification
   - Check acceptance criteria are specific and testable
   - Ensure all dependencies are met
   - Validate file format with GitHub Actions

3. **Workflow Failures**
   ```bash
   # Check GitHub Actions logs
   # Go to Actions tab in GitHub
   # Look for failed runs and check logs
   
   # Validate local file format
   find .ai/tasks -name "*.md" -exec yamllint {} \;
   ```

## 🎯 Success Metrics

Track these KPIs weekly:
- **Agent Task Success Rate**: % of agent tasks completed without human intervention
- **Sprint Velocity**: Average tasks completed per sprint
- **Cycle Time**: Average time from backlog → done
- **Quality Score**: % of tasks passing review on first attempt

## 🚀 Next Steps

1. **Week 1**: Set up basic structure and sync first tasks
2. **Week 2**: Implement first sprint with mixed agent/human tasks
3. **Week 3**: Optimize agent task patterns based on success rates
4. **Week 4**: Scale to full DocStream-Cloud roadmap

## 📚 Additional Resources

- [GitHub Projects v2 API Documentation](https://docs.github.com/en/issues/planning-and-tracking-with-projects/automating-your-project/using-the-api-to-manage-projects)
- [GitHub Actions Workflow Syntax](https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions)
- [AI Agent Best Practices](link-to-internal-docs)

---

**Questions?** Create an issue with the `question` label or reach out to the development team. 