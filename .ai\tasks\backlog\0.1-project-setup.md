---
phase: 0.1
title: "Initial Project Setup and Configuration"
classification: AGENT_READY
complexity: Medium
dependencies: []
---

# Initial Project Setup and Configuration

## Overview

Set up the foundational structure for the StudyScore.info Next.js frontend application with TypeScript, Tailwind CSS, and other essential configurations.

## Acceptance Criteria

- [x] Create the recommended folder structure for the Next.js application
- [x] Configure Next.js with TypeScript support
- [x] Set up Tailwind CSS with proper theming
- [x] Configure Biome for linting and formatting
- [x] Set up essential utility files and components
- [x] Ensure all configuration files are properly typed
- [x] Implement a basic layout and homepage
- [x] Verify the development server runs without errors

## Implementation Guide

### 1. Folder Structure

Create the following folder structure:

```
studyscore-website/
├── .ai/                  # Already exists
├── public/               # Static assets
├── src/
│   ├── app/              # Next.js App Router
│   ├── components/       # UI components
│   │   ├── ui/           # Base UI components
│   │   └── features/     # Feature-specific components
│   ├── hooks/            # Custom React hooks
│   ├── lib/              # Utility functions
│   └── types/            # TypeScript type definitions
├── .env.example          # Example environment variables
├── .gitignore            # Git ignore file
├── biome.json            # Biome configuration
├── next.config.mjs       # Next.js configuration
├── package.json          # Project dependencies
├── postcss.config.mjs    # PostCSS configuration
├── tailwind.config.ts    # Tailwind CSS configuration
└── tsconfig.json         # TypeScript configuration
```

### 2. Configuration Files

#### package.json

Create a `package.json` file with the following content:

```json
{
  "name": "studyscore-website",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "next dev --turbopack",
    "build": "next build",
    "start": "next start",
    "lint": "biome lint ./src",
    "format": "biome format --write .",
    "typecheck": "tsc --noEmit",
    "check": "biome check --apply .",
    "clean": "git clean -xdf node_modules .next"
  },
  "dependencies": {
    "@ai-sdk/vercel": "^0.0.1",
    "@vercel/mcp-adapter": "^0.10.0",
    "@vercel/sdk": "^1.8.1",
    "ai": "^4.3.16",
    "clsx": "^2.1.1",
    "cmdk": "^1.0.0",
    "dotenv": "^16.5.0",
    "lucide-react": "^0.408.0",
    "next": "^15.3.3",
    "react": "^19.1.0",
    "react-dom": "^19.1.0",
    "tailwind-merge": "^2.4.0",
    "tailwindcss": "^4.1.8",
    "tailwindcss-animate": "^1.0.7",
    "zod": "^3.25.63"
  },
  "devDependencies": {
    "@biomejs/biome": "1.9.4",
    "@types/node": "20.11.20",
    "@types/react": "18.2.57",
    "autoprefixer": "^10.4.21",
    "eslint-config-next": "14.2.0",
    "typescript": "^5.8.3"
  }
}
```

#### next.config.mjs

Create a `next.config.mjs` file with the following content:

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: [], // Add domains for external images if needed
    formats: ['image/avif', 'image/webp'],
  },
  experimental: {
    serverActions: true,
  },
};

export default nextConfig;
```

#### tailwind.config.ts

Create a `tailwind.config.ts` file with the following content:

```typescript
import type { Config } from "tailwindcss";
import { fontFamily } from "tailwindcss/defaultTheme";

const config: Config = {
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        "brand-blue": "#00D2FF",
        "accent-green": "#80FF00",
        "warning-yellow": "#FFD200",
        "alert-pink": "#FF00A7",
        "gray-light": "#F5F5F5",
        "gray-medium": "#E6E6E6",
        "gray-dark": "#CCCCCC",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      fontFamily: {
        sans: ["var(--font-sans)", ...fontFamily.sans],
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};

export default config;
```

#### postcss.config.mjs

Create a `postcss.config.mjs` file with the following content:

```javascript
const config = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
};

export default config;
```

#### biome.json

Create a `biome.json` file with the following content:

```json
{
  "$schema": "https://biomejs.dev/schemas/1.4.1/schema.json",
  "organizeImports": {
    "enabled": true
  },
  "linter": {
    "enabled": true,
    "rules": {
      "recommended": true,
      "correctness": {
        "noUnusedVariables": "error"
      },
      "suspicious": {
        "noExplicitAny": "error"
      },
      "style": {
        "noNonNullAssertion": "error"
      }
    }
  },
  "formatter": {
    "enabled": true,
    "indentStyle": "space",
    "indentWidth": 2,
    "lineWidth": 100
  },
  "javascript": {
    "formatter": {
      "quoteStyle": "single",
      "trailingComma": "all",
      "semicolons": "always"
    }
  }
}
```

#### tsconfig.json

Create a `tsconfig.json` file with the following content:

```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

### 3. Essential Files

#### src/lib/utils.ts

Create a `src/lib/utils.ts` file with the following content:

```typescript
import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(input: string | number | Date): string {
  const date = new Date(input);
  return date.toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric',
  });
}

export function absoluteUrl(path: string) {
  return `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}${path}`;
}
```

#### src/app/globals.css

Create a `src/app/globals.css` file with the following content:

```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
```

#### src/app/layout.tsx

Create a `src/app/layout.tsx` file with the following content:

```tsx
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-sans',
});

export const metadata: Metadata = {
  title: 'StudyScore',
  description: 'Sports Science Research Knowledge Graph Platform',
  keywords: ['research', 'sports science', 'knowledge graph', 'papers'],
  authors: [
    {
      name: 'StudyScore Team',
    },
  ],
  creator: 'StudyScore',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`min-h-screen bg-background font-sans antialiased ${inter.variable}`}>
        {children}
      </body>
    </html>
  );
}
```

#### src/app/page.tsx

Create a `src/app/page.tsx` file with the following content:

```tsx
export default function Home() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-24">
      <div className="z-10 max-w-5xl w-full items-center justify-center font-mono text-sm">
        <h1 className="text-4xl font-bold text-center mb-8">
          Welcome to <span className="text-brand-blue">StudyScore</span>
        </h1>
        <p className="text-center text-lg mb-4">
          Sports Science Research Knowledge Graph Platform
        </p>
        <div className="flex justify-center mt-8">
          <a
            href="/dashboard"
            className="px-4 py-2 rounded-md bg-primary text-primary-foreground hover:bg-primary/90"
          >
            Get Started
          </a>
        </div>
      </div>
    </main>
  );
}
```

#### .env.example

Create a `.env.example` file with the following content:

```
# App
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Add other environment variables as needed
```

#### .gitignore

Create a `.gitignore` file with the following content:

```
# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
```

## Test Requirements

1. **Development Server Test**:
   - Run `npm run dev` and verify the server starts without errors
   - Navigate to http://localhost:3000 and confirm the homepage loads correctly

2. **TypeScript Check**:
   - Run `npm run typecheck` to ensure there are no TypeScript errors

3. **Linting Test**:
   - Run `npm run lint` to verify there are no linting errors

4. **Formatting Test**:
   - Run `npm run format` to ensure code is properly formatted

## Definition of Done

- [ ] All folder structure is created according to the implementation guide
- [ ] All configuration files are created with the specified content
- [ ] All essential files are created with the specified content
- [ ] Development server starts without errors
- [ ] TypeScript check passes without errors
- [ ] Linting check passes without errors
- [ ] Formatting check passes without errors
- [ ] Homepage renders correctly in the browser
- [ ] All dependencies are installed and working correctly

## Branch Name

`feat/0.1-project-setup`

## Notes for AI Agent

- Make sure to install all dependencies with `npm install` after creating the configuration files
- The project uses Next.js 15+ with the App Router
- Tailwind